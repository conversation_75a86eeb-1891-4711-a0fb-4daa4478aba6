package fr.enedis.i2r.system.watchdog;

import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;

import java.lang.reflect.Field;
import java.time.Duration;
import java.time.Instant;
import java.util.concurrent.ConcurrentMap;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ScheduledFuture;
import java.util.concurrent.TimeUnit;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.Timeout;

@SuppressWarnings("unchecked")
class ThreadWatchdogTest {

    private ThreadWatchdog threadWatchdog;

    @BeforeEach
    void setUp() {
        threadWatchdog = new ThreadWatchdog();
    }

    @AfterEach
    void tearDown() {
        threadWatchdog.shutdown();
    }

    @Test
    void un_thread_est_bien_enregistre_et_desenregistre() {
        String threadName = "test-thread";

        assertDoesNotThrow(() -> threadWatchdog.register(threadName));
        assertDoesNotThrow(() -> threadWatchdog.unregister(threadName));
    }

    @Test
    void plusieurs_threads_sont_bien_enregistres_et_desenregistres() {
        assertDoesNotThrow(() -> {
            threadWatchdog.register("thread1");
            threadWatchdog.register("thread2");
            threadWatchdog.register("thread3");

            threadWatchdog.unregister("thread1");
            threadWatchdog.unregister("thread2");
            threadWatchdog.unregister("thread3");
        });
    }

    @Test
    void l_interval_entre_les_heartbeats_peut_etre_modifie() {
        Duration newInterval = Duration.ofSeconds(30);
        assertDoesNotThrow(() -> threadWatchdog.setHeartbeatInterval(newInterval));

        // Register a thread to verify it works with the new interval
        assertDoesNotThrow(() -> threadWatchdog.register("test-thread-with-custom-interval"));
        assertDoesNotThrow(() -> threadWatchdog.unregister("test-thread-with-custom-interval"));
    }

    @Test
    void unregister_thread_inexistant_ne_leve_pas_exception() {
        // Unregistering a non-existent thread should not throw an exception
        assertDoesNotThrow(() -> threadWatchdog.unregister("non-existent-thread"));
    }

    @Test
    void les_threads_peuvent_etre_enregistres_plusieurs_fois() {
        String threadName = "duplicate-thread";

        // Register the same thread multiple times - should not cause issues
        assertDoesNotThrow(() -> {
            threadWatchdog.register(threadName);
            threadWatchdog.register(threadName); // Should replace the previous registration
            threadWatchdog.unregister(threadName);
        });
    }

    @Test
    void les_threads_sont_bien_nettoyes() throws Exception {
        threadWatchdog.register("thread1");
        threadWatchdog.register("thread2");
        threadWatchdog.register("thread3");

        // Verify we can register new threads after clearing (no conflicts)
        assertDoesNotThrow(() -> {
            threadWatchdog.register("new-thread");
            threadWatchdog.unregister("new-thread");
        });
    }

    @Test
    @Timeout(10)
    void heartbeat_fonctionne_correctement() throws Exception {
        String threadName = "heartbeat-test-thread";
        threadWatchdog.setHeartbeatInterval(Duration.ofSeconds(1));

        threadWatchdog.register(threadName);

        ConcurrentMap<String, Instant> threads = threadWatchdog.getThreads();

        Instant initialTime = threads.get(threadName);
        assertNotNull(initialTime, "Thread should be registered with initial timestamp");

        // Wait for at least one heartbeat cycle (heartbeat interval is 1 second)
        Thread.sleep(1500);

        Instant afterHeartbeat = threads.get(threadName);
        assertNotNull(afterHeartbeat, "Thread should still be registered after heartbeat");
        assertTrue(afterHeartbeat.isAfter(initialTime), "Heartbeat should update the timestamp");

        threadWatchdog.unregister(threadName);
    }

    @Test
    @Timeout(15)
    void timeout_checker_demarre_automatiquement() throws Exception {
        String threadName = "timeout-checker-test";

        // Register a thread which should start the timeout checker
        threadWatchdog.register(threadName);

        // Check if timeout checker is running
        Field timeoutCheckerField = ThreadWatchdog.class.getDeclaredField("timeoutChecker");
        timeoutCheckerField.setAccessible(true);
        ScheduledFuture<?> timeoutChecker = (ScheduledFuture<?>) timeoutCheckerField.get(threadWatchdog);

        assertNotNull(timeoutChecker, "Timeout checker should be started");
        assertFalse(timeoutChecker.isCancelled(), "Timeout checker should be running");

        threadWatchdog.unregister(threadName);
    }

    @Test
    @Timeout(10)
    void les_heartbeats_peuvent_etre_crees_et_annules() throws Exception {
        String threadName = "scheduled-heartbeat-test";

        Field scheduledHeartbeatsField = ThreadWatchdog.class.getDeclaredField("scheduledHeartbeats");
        scheduledHeartbeatsField.setAccessible(true);
        ConcurrentMap<String, ScheduledFuture<?>> scheduledHeartbeats =
            (ConcurrentMap<String, ScheduledFuture<?>>) scheduledHeartbeatsField.get(threadWatchdog);

        threadWatchdog.register(threadName);

        // Check that scheduled heartbeat was created
        assertTrue(scheduledHeartbeats.containsKey(threadName), "Scheduled heartbeat should be created");
        ScheduledFuture<?> heartbeatFuture = scheduledHeartbeats.get(threadName);
        assertNotNull(heartbeatFuture, "Heartbeat future should not be null");
        assertFalse(heartbeatFuture.isCancelled(), "Heartbeat should be running");

        threadWatchdog.unregister(threadName);

        // Check that scheduled heartbeat was cancelled and removed
        assertFalse(scheduledHeartbeats.containsKey(threadName), "Scheduled heartbeat should be removed");
        assertTrue(heartbeatFuture.isCancelled(), "Heartbeat should be cancelled");
    }

    @Test
    @Timeout(10)
    void les_operations_concurrentes_sont_thread_safe() throws Exception {
        int threadCount = 10;
        CountDownLatch startLatch = new CountDownLatch(1);
        CountDownLatch doneLatch = new CountDownLatch(threadCount);

        // Create multiple threads that register/unregister concurrently
        for (int i = 0; i < threadCount; i++) {
            final int threadIndex = i;
            new Thread(() -> {
                try {
                    startLatch.await();
                    String threadName = "concurrent-thread-" + threadIndex;
                    threadWatchdog.register(threadName);
                    Thread.sleep(100); // Simulate some work
                    threadWatchdog.unregister(threadName);
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                } finally {
                    doneLatch.countDown();
                }
            }).start();
        }

        // Start all threads simultaneously
        startLatch.countDown();

        // Wait for all threads to complete
        assertTrue(doneLatch.await(5, TimeUnit.SECONDS), "All threads should complete");

        // Verify no threads are left registered
        ConcurrentMap<String, Instant> threads = threadWatchdog.getThreads();

        assertEquals(0, threads.size(), "No threads should remain registered");
    }
}
